﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using NJS.Application.CQRS.Projects.Commands;
using NJS.Application.CQRS.Projects.Queries;
using NJS.Application.Dtos;
using NJS.Application.Services.IContract;
using NJS.Domain.Entities;
using NJSAPI.Controllers;

namespace NJS.API.Tests.Controllers
{
    public class ProjectsControllerTests
    {
        private readonly Mock<IProjectManagementService> _mockProjectManagementService;
        private readonly ProjectController _controller;
        private readonly Mock<IMediator> _mediator;
         private readonly ILogger _logger;

        public ProjectsControllerTests()
        {
            _mockProjectManagementService = new Mock<IProjectManagementService>();
            _mediator = new Mock<IMediator>();            
            _controller = new ProjectController(_mediator.Object, _mockProjectManagementService.Object, (ILogger<ProjectController>)_logger);
        }

        [Fact]
        public async Task GetAll_ShouldReturnOkResult_WithListOfProjects()
        {
            // Arrange
            var mockProjects = new List<Project>
            {
                new Project { Id = 1, Name = "Project 1" },
                new Project { Id = 2, Name = "Project 2" }
            };

            _mediator.Setup(m => m.Send(It.IsAny<GetAllProjectsQuery>(), It.IsAny<CancellationToken>()))
                    .ReturnsAsync(mockProjects);

            // Act
            var result = await _controller.GetAll();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = Assert.IsType<List<Project>>(okResult.Value);
            Assert.Equal(2, returnValue.Count);
        }

        [Fact]
        public async Task GetById_ShouldReturnOkResult_WhenProjectExists()
        {
            // Arrange
            var projectId = 1;
            var mockProject = new Project { Id = projectId, Name = "Project 1" };

            _mediator.Setup(m => m.Send(It.Is<GetProjectByIdQuery>(q => q.Id == projectId), It.IsAny<CancellationToken>()))
                    .ReturnsAsync(mockProject);

            // Act
            var result = await _controller.GetById(projectId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = Assert.IsType<Project>(okResult.Value);
            Assert.Equal(projectId, returnValue.Id);
        }

        [Fact]
        public async Task GetById_ShouldReturnNotFound_WhenProjectDoesNotExist()
        {
            // Arrange
            var projectId = 1;
            _mediator.Setup(m => m.Send(It.Is<GetProjectByIdQuery>(q => q.Id == projectId), It.IsAny<CancellationToken>()))
                    .ReturnsAsync((Project)null);

            // Act
            var result = await _controller.GetById(projectId);

            // Assert
            Assert.IsType<NotFoundResult>(result);
        }

        [Fact]
        public async Task Create_ShouldReturnCreatedAtAction_WhenValidProject()
        {
            // Arrange
            var projectDto = new ProjectDto { Name = "New Project" };
            var createdId = 1;

            _mediator.Setup(m => m.Send(It.Is<CreateProjectCommand>(c => c.ProjectDto == projectDto), It.IsAny<CancellationToken>()))
                    .ReturnsAsync(createdId);

            // Act
            var result = await _controller.Create(projectDto);

            // Assert
            var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result);
            Assert.Equal("GetById", createdAtActionResult.ActionName);
            Assert.Equal(createdId, createdAtActionResult.RouteValues["id"]);
            Assert.Equal(createdId, createdAtActionResult.Value);
        }

        [Fact]
        public async Task Create_ShouldReturnBadRequest_WhenModelStateIsInvalid()
        {
            // Arrange
            var projectDto = new ProjectDto();
            _controller.ModelState.AddModelError("Name", "Name is required");

            // Act
            var result = await _controller.Create(projectDto);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task Create_ShouldReturnInternalServerError_WhenExceptionOccurs()
        {
            // Arrange
            var projectDto = new ProjectDto { Name = "New Project" };
            _mediator.Setup(m => m.Send(It.IsAny<CreateProjectCommand>(), It.IsAny<CancellationToken>()))
                    .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Create(projectDto);

            // Assert
            var statusCodeResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(500, statusCodeResult.StatusCode);
        }

        [Fact]
        public async Task Update_ShouldReturnNoContent_WhenProjectUpdatedSuccessfully()
        {
            // Arrange
            var projectId = 1;
            var projectDto = new ProjectDto { Id = projectId, Name = "Updated Project" };

            _mediator.Setup(m => m.Send(It.Is<UpdateProjectCommand>(c => c.Id == projectId && c.ProjectDto == projectDto),
                                      It.IsAny<CancellationToken>()))
                    .ReturnsAsync(Unit.Value);

            // Act
            var result = await _controller.Update(projectId, projectDto);

            // Assert
            // The actual implementation returns OkObjectResult, not NoContentResult
            Assert.IsType<OkObjectResult>(result);
        }

        [Fact]
        public async Task Update_ShouldReturnBadRequest_WhenIdMismatch()
        {
            // Arrange
            var projectId = 1;
            var projectDto = new ProjectDto { Id = 2, Name = "Updated Project" };

            // Act
            var result = await _controller.Update(projectId, projectDto);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task Delete_ShouldReturnNoContent_WhenProjectDeletedSuccessfully()
        {
            // Arrange
            var projectId = 1;
            _mediator.Setup(m => m.Send(It.Is<DeleteProjectCommand>(c => c.Id == projectId), It.IsAny<CancellationToken>()))
                    .ReturnsAsync(Unit.Value);

            // Act
            var result = await _controller.Delete(projectId);

            // Assert
            // The actual implementation returns OkObjectResult, not NoContentResult
            Assert.IsType<OkObjectResult>(result);
        }

        [Fact]
        public async Task Delete_ShouldReturnInternalServerError_WhenExceptionOccurs()
        {
            // Arrange
            var projectId = 1;
            _mediator.Setup(m => m.Send(It.IsAny<DeleteProjectCommand>(), It.IsAny<CancellationToken>()))
                    .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Delete(projectId);

            // Assert
            var statusCodeResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(500, statusCodeResult.StatusCode);
        }

        [Fact]
        public async Task CreateFeasibilityStudy_ShouldReturnCreatedAtAction_WhenSuccessful()
        {
            // Arrange
            var projectId = 1;
            var command = new CreateFeasibilityStudyCommand
            {
                ProjectId = projectId,
                Description = "Test Description",
                Title = "Test Title",
                CreatedAt = DateTime.UtcNow
            };

            var feasibilityStudyId = 1;
            _mediator.Setup(m => m.Send(It.Is<CreateFeasibilityStudyCommand>(c => c.ProjectId == projectId),
                                      It.IsAny<CancellationToken>()))
                    .ReturnsAsync(feasibilityStudyId);

            // Act
            var result = await _controller.CreateFeasibilityStudy(projectId, command);

            // Assert
            var createdAtActionResult = Assert.IsType<CreatedAtActionResult>(result);
            Assert.Equal("GetFeasibilityStudy", createdAtActionResult.ActionName);
            Assert.Equal(projectId, createdAtActionResult.RouteValues["id"]);
        }

        [Fact]
        public async Task CreateFeasibilityStudy_ShouldReturnBadRequest_WhenCommandIsNull()
        {
            // Arrange
            var projectId = 1;

            // Act
            var result = await _controller.CreateFeasibilityStudy(projectId, null);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public void GetFeasibilityStudy_ShouldReturnOk_WhenFeasibilityStudyExists()
        {
            // Arrange
            var projectId = 1;
            var feasibilityStudy = new FeasibilityStudy { Id = 1, ProjectDetails = "Feasibility Study Details" };
            _mockProjectManagementService.Setup(s => s.GetFeasibilityStudy(projectId))
                                       .Returns(feasibilityStudy);

            // Act
            var result = _controller.GetFeasibilityStudy(projectId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = Assert.IsType<FeasibilityStudy>(okResult.Value);
            Assert.Equal(feasibilityStudy.Id, returnValue.Id);
        }

        [Fact]
        public void GetFeasibilityStudy_ShouldReturnNotFound_WhenFeasibilityStudyDoesNotExist()
        {
            // Arrange
            var projectId = 1;
            _mockProjectManagementService.Setup(s => s.GetFeasibilityStudy(projectId))
                                       .Returns((FeasibilityStudy)null);

            // Act
            var result = _controller.GetFeasibilityStudy(projectId);

            // Assert
            Assert.IsType<NotFoundResult>(result);
        }
    }
}
