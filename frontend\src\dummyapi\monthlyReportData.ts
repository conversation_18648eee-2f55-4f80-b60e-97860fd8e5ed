export const monthlyReportData = {
    "financialAndContractDetails": {
        "net": 724533,
        "serviceTax": 18,
        "feeTotal": 854948.94,
        "budgetOdcs": 11592,
        "budgetStaff": 59880,
        "BudgetSubTotal": 71472,
        "contractType": "lumpsum",
        "percentage": 23
    },
    "actualCost": {
        "priorCumulativeOdc": 300,
        "priorCumulativeStaff": 400,
        "priorCumulativeTotal": 700,
        "actualOdc": 700,
        "actualStaff": 800,
        "actualSubtotal": 1500,
        "totalCumulativeOdc": 1000,
        "totalCumulativeStaff": 1200,
        "totalCumulativeCost": 2200
    },
    "ctcAndEac": {
        "ctcODC": 10892,
        "ctcStaff": 59080,
        "ctcSubtotal": 69972,
        "actualctcODC": 10900,
        "actualCtcStaff": 59100,
        "actualCtcSubtotal": 70000,
        "totalEAC": 71472,
        "grossProfitPercentage": 90.14
    },
    "schedule": {
        "dateOfIssueWOLOI": "2025-07-04T10:15:34.313Z",
        "completionDateAsPerContract": "2025-07-04T10:15:34.313Z",
        "completionDateAsPerExtension": "2025-07-04T10:15:34.313Z",
        "expectedCompletionDate": "2025-07-04T10:15:34.313Z"
    },
    "budgetTable": {
        "originalBudget": {
            "revenueFee": 724533,
            "cost": 653061,
            "profitPercentage": 724533
        },
        "currentBudgetInMIS": {
            "revenueFee": 724533,
            "cost": 71472,
            "profitPercentage": 90.14
        },
        "percentCompleteOnCosts": {
            "revenueFee": 4.14,
            "cost": 41.97
        }
    },
    "manpowerPlanning": {
        "manpower": [
            {
                "workAssignment": "topographical_survey",
                "assignee": "Mnjunath Gowda",
                "planned": 160,
                "consumed": 140,
                "balance": 20,
                "nextMonthPlanning": 67,
                "manpowerComments": "First"
            }
        ],
        "manpowerTotal": {
            "plannedTotal": 160,
            "consumedTotal": 140,
            "balanceTotal": 20,
            "nextMonthPlanningTotal": 67
        }
    },
    "progressDeliverable": {
        "deliverables": [
            {
                "milestone": "First Payment",
                "dueDateContract": "2025-07-04T10:16:34.138Z",
                "dueDatePlanned": "2025-07-04T10:16:34.138Z",
                "achievedDate": "2025-07-04T10:16:34.138Z",
                "paymentDue": 10000,
                "invoiceDate": "2025-07-04T10:16:34.138Z",
                "paymentReceivedDate": "2025-07-04T10:16:34.138Z",
                "deliverableComments": ""
            },
            {
                "milestone": "Second Payment",
                "dueDateContract": "2025-07-04T10:16:56.466Z",
                "dueDatePlanned": "2025-07-04T10:16:56.466Z",
                "achievedDate": "2025-07-04T10:16:56.466Z",
                "paymentDue": 20000,
                "invoiceDate": "2025-07-04T10:16:56.466Z",
                "paymentReceivedDate": "2025-07-04T10:16:56.466Z",
                "deliverableComments": ""
            }
        ],
        "totalPaymentDue": 30000
    },
    "changeOrder": [
        {
            "contractTotal": 17891,
            "cost": 17892,
            "fee": 17893,
            "summaryDetails": "Change order 1",
            "status": "Proposed"
        },
        {
            "contractTotal": 17892,
            "cost": 17893,
            "fee": 17894,
            "summaryDetails": "change order 2",
            "status": "Approved"
        }
    ],
    "programmeSchedule": [
        {
            "ProgrammeDescription": "No Programme Schedules for this project 1."
        },
        {
            "ProgrammeDescription": "No Programme Schedules for this project 2."
        }
    ],
    "earlyWarnings": [
        {
            "WarningsDescription": "No Early Warnings for this project 1."
        },
        {
            "WarningsDescription": "No Early Warnings for this project 2."
        }
    ],
    "lastMonthActions": [
        {
            "LMactions": "Last Action 1",
            "LMAdate": "2025-07-04T10:20:27.054Z",
            "LMAcomments": "Last Action comment 1"
        },
        {
            "LMactions": "Last Action 2",
            "LMAdate": "2025-07-04T10:20:54.993Z",
            "LMAcomments": "Last Action comment 2"
        }
    ],
    "currentMonthActions": [
        {
            "CMactions": "current Action 1",
            "CMAdate": "2025-07-04T10:21:11.757Z",
            "CMAcomments": "current Action comment 1",
            "CMApriority": "H"
        },
        {
            "CMactions": "current Action 2",
            "CMAdate": "2025-07-04T10:21:33.731Z",
            "CMAcomments": "current Action comment 2",
            "CMApriority": "M"
        }
    ]
}
